<?php
/**
 * 测试 yid 覆盖问题修复
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "测试 yid 覆盖问题修复...\n";

// 包含必要文件
include('confing/common.php');

echo "1. 查询有正确订单号的易教育订单...\n";

// 查询有正确订单号的易教育订单
$sql = "SELECT o.oid, o.user, o.status, o.process, o.yid, h.pt 
        FROM qingka_wangke_order o 
        LEFT JOIN qingka_wangke_huoyuan h ON o.hid = h.hid 
        WHERE h.pt = 'jxjy' 
        AND o.yid != '1' 
        AND o.yid != '' 
        AND o.yid IS NOT NULL
        ORDER BY o.addtime DESC 
        LIMIT 3";

$result = $DB->query($sql);
$test_orders = [];

if ($result) {
    while ($order = $DB->fetch($result)) {
        $test_orders[] = $order;
    }
}

if (empty($test_orders)) {
    echo "❌ 没有找到有正确订单号的易教育订单\n";
    
    // 查询所有易教育订单
    $sql2 = "SELECT o.oid, o.user, o.status, o.process, o.yid, h.pt 
             FROM qingka_wangke_order o 
             LEFT JOIN qingka_wangke_huoyuan h ON o.hid = h.hid 
             WHERE h.pt = 'jxjy' 
             ORDER BY o.addtime DESC 
             LIMIT 3";
    
    $result2 = $DB->query($sql2);
    if ($result2) {
        while ($order = $DB->fetch($result2)) {
            $test_orders[] = $order;
        }
    }
    
    if (!empty($test_orders)) {
        echo "使用所有易教育订单进行测试...\n";
    }
}

if (!empty($test_orders)) {
    echo "找到 " . count($test_orders) . " 个订单进行测试\n";
    
    foreach ($test_orders as $order) {
        echo "\n2. 测试订单 {$order['oid']}...\n";
        echo "   用户: {$order['user']}\n";
        echo "   当前状态: {$order['status']}\n";
        echo "   当前进度: {$order['process']}\n";
        echo "   当前订单号: {$order['yid']}\n";
        
        // 记录原始的 yid
        $original_yid = $order['yid'];
        
        // 模拟同步更新（不实际调用API，只测试更新逻辑）
        echo "   模拟同步更新...\n";
        
        // 模拟 processCx 返回的数据
        $mock_item = array(
            'code' => 1,
            'status_text' => '进行中',
            'process' => '50%',
            'remarks' => '测试同步',
            'yid' => '1'  // 模拟错误的返回值
        );
        
        // 应用修复后的逻辑
        $status = $mock_item['status_text'];
        $process = $mock_item['process'];
        $remarks = $mock_item['remarks'];
        
        // 🔥 关键修复：不要覆盖 yid 字段！
        $should_update_yid = false;
        $new_yid = $order['yid']; // 默认保持原值
        
        if (empty($order['yid']) || $order['yid'] == '1') {
            if (isset($mock_item['yid']) && !empty($mock_item['yid']) && $mock_item['yid'] != '1') {
                $should_update_yid = true;
                $new_yid = $mock_item['yid'];
            }
        }
        
        echo "   原始订单号: {$original_yid}\n";
        echo "   API返回订单号: {$mock_item['yid']}\n";
        echo "   是否更新yid: " . ($should_update_yid ? "是" : "否") . "\n";
        echo "   最终订单号: {$new_yid}\n";
        
        if ($should_update_yid) {
            echo "   ✅ 逻辑正确：会更新 yid（因为原值为空或为1）\n";
        } else {
            echo "   ✅ 逻辑正确：保持原 yid 不变（防止覆盖）\n";
        }
        
        // 验证逻辑
        if ($original_yid != '1' && $original_yid != '' && $new_yid == $original_yid) {
            echo "   ✅ 修复成功：正确的订单号被保护\n";
        } elseif (($original_yid == '1' || $original_yid == '') && $should_update_yid) {
            echo "   ✅ 修复成功：异常订单号会被更新\n";
        } else {
            echo "   ⚠️ 需要检查逻辑\n";
        }
    }
} else {
    echo "❌ 没有找到易教育订单进行测试\n";
}

echo "\n3. 检查易教育货源配置...\n";
$jk = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt='jxjy' LIMIT 1");
if ($jk) {
    echo "✅ 易教育货源配置存在:\n";
    echo "  货源ID: {$jk['hid']}\n";
    echo "  货源名称: {$jk['name']}\n";
    echo "  API地址: {$jk['url']}\n";
    echo "  Token状态: " . (empty($jk['token']) ? "❌ 未设置" : "✅ 已设置") . "\n";
} else {
    echo "❌ 易教育货源配置不存在\n";
}

echo "\n🎉 yid 覆盖问题修复测试完成！\n";
echo "\n修复要点:\n";
echo "1. 同步时不会覆盖正确的订单号\n";
echo "2. 只有当原订单号为空或为'1'时，才会使用API返回的订单号\n";
echo "3. 保护已有的正确订单号不被错误覆盖\n";

?>

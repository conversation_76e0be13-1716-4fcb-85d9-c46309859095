<?php
/**
 * 易教育API接口文件 - 完整版本
 * 支持查课、下单、订单列表等功能
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');

// 获取请求参数
$act = $_GET['act'] ?? $_POST['act'] ?? '';

// 获取易教育货源配置
$hid = '';
$jk = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' OR instr(name,'易教育') LIMIT 1");
if (!$jk) {
    exit(json_encode(array("code" => -1, "msg" => "未找到易教育货源配置")));
}

// 设置请求头
$header = ["Content-Type: application/json; charset=utf-8", "Authorization: Bearer " . $jk['token']];
$now_time = date("Y-m-d H:i:s");

// 通用POST请求函数（检查是否已定义）
if (!function_exists('post')) {
    function post($url, $data, $headers = array()) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $result = curl_exec($ch);
        curl_close($ch);
        return $result;
    }
}

// 通用JSON返回函数
function jsonReturn($code, $msg, $data = null) {
    $result = array("code" => $code, "msg" => $msg);
    if ($data !== null) {
        $result["data"] = $data;
    }
    exit(json_encode($result));
}

switch ($act) {
    case 'jxjyclass':
        // 获取易教育项目列表 - 从主商品表获取易教育相关项目
        $a = $DB->query("SELECT * FROM qingka_wangke_class WHERE status=1 AND docking='{$jk['hid']}' ORDER BY cid ASC");
        $data = array();
        while ($row = $DB->fetch($a)) {
            $price = $row['price'] * $userrow['addprice'];
            $formattedPrice = number_format($price, 2);
            $row['price'] = $formattedPrice;
            $data[] = $row;
        }
        $response = array('code' => 1, 'data' => $data);
        exit(json_encode($response));
        break;

    case 'getcourse':
        // 查课功能
        $id = trim(strip_tags(daddslashes($_POST['id'])));
        $username = trim(strip_tags(daddslashes($_POST['user'])));
        $password = trim(strip_tags(daddslashes($_POST['pass'])));

        // 从主商品表查询项目信息（前端传递的是cid）
        $rs = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid='$id' LIMIT 1");

        // 检查项目是否存在
        if (!$rs) {
            jsonReturn(-1, "项目信息不存在，项目ID: {$id}。请检查项目是否为易教育项目。");
        }

        // 检查是否为易教育项目
        if ($rs['docking'] != $jk['hid']) {
            jsonReturn(-1, "该项目不是易教育项目，无法查课。");
        }

        // 使用noun字段作为易教育的项目编号
        $number = $rs['noun'];

        wlog($userrow['uid'], "继续教育查课", "平台{$rs['name']} {$username} {$password}", 0);

        // 易教育项目默认支持查课，这里可以根据需要添加判断逻辑
        // 暂时注释掉，因为主商品表没有isSearchCourse字段
        // if ($rs['isSearchCourse'] == '0') {
        //     jsonReturn(-1, "该项目无需查课");
        // }

        $data = array(
            "websiteNumber" => $number,
            "data" => array(array("username" => $username, "password" => $password))
        );
        $data = json_encode($data);
        $jxjy_url = $jk["url"] . "/api/website/queryCourse";
        $result = post($jxjy_url, $data, $header);
        $result = json_decode($result, true);

        if ($result['code'] == 200) {
            $uuid = $result['data']['uuid'];
            // 延时5秒
            sleep(5);
            $data2 = array("uuid" => $uuid);
            $data2 = json_encode($data2);
            $jxjy_url2 = $jk["url"] . "/api/website/getQueryCourse";
            $result2 = post($jxjy_url2, $data2, $header);
            $result2 = json_decode($result2, true);

            if ($result2['code'] == 200 && strpos($result2['data'][0]['name'], "登录失败") === false) {
                if (isset($result2['data'][0]['children'])) {
                    $children = $result2['data'][0]['children'];
                    $filteredData = array();
                    foreach ($children as $child) {
                        if (isset($child['id']) && isset($child['name'])) {
                            $filteredData[] = array("id" => $child['id'], "name" => $child['name']);
                        } elseif (isset($child['name']) && isset($child['children'])) {
                            foreach ($child['children'] as $subChild) {
                                if (isset($subChild['id']) && isset($subChild['name'])) {
                                    $filteredData[] = array("id" => $subChild['id'], "name" => $child['name'] . "----" . $subChild['name']);
                                }
                            }
                        }
                    }
                    $responseData = array(array("code" => 1, "data" => $filteredData, "msg" => "查询成功", "userinfo" => $result2['data'][0]['name']));
                    exit(json_encode($responseData));
                }
            } else {
                $responseData = array(array("code" => -1, "msg" => $result2['data'][0]['name']));
                exit(json_encode($responseData));
            }
        } else {
            $responseData = array(array("code" => -1, "msg" => '发送查课请求失败，请联系管理员'));
            exit(json_encode($responseData));
        }
        break;

    case 'jxjyadd':
        // 下单功能
        $id = trim(strip_tags(daddslashes($_POST['id'])));
        $username = trim(strip_tags(daddslashes($_POST['user'])));
        $password = trim(strip_tags(daddslashes($_POST['pass'])));
        $select = $_POST['select'];

        // 从主商品表查询项目信息（前端传递的是cid）
        $rs = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid='$id' LIMIT 1");

        // 检查项目是否存在
        if (!$rs) {
            jsonReturn(-1, "项目信息不存在，项目ID: {$id}。请检查项目是否为易教育项目。");
        }

        // 检查是否为易教育项目
        if ($rs['docking'] != $jk['hid']) {
            jsonReturn(-1, "该项目不是易教育项目，无法下单。");
        }

        // 使用noun字段作为易教育的项目编号
        $number = $rs['noun'];

        if ($id == '') {
            jsonReturn(-1, "ID不能为空");
        }
        if ($username == '') {
            jsonReturn(-1, "账号不能为空");
        }
        if ($password == '') {
            jsonReturn(-1, "密码不能为空");
        }

        // 判断是否需要查课（暂时默认所有易教育项目都支持查课）
        $need_course_selection = !empty($select); // 如果有选择课程，说明是查课下单

        if (!$need_course_selection) {
            // 无需查课的项目（直接下单）
            $danjia = round($rs['price'] * $userrow['addprice'], 2);
            if ($userrow['money'] < $danjia) {
                jsonReturn(-1, "您的余额不足");
            }
            if ($danjia == 0 || $userrow['addprice'] < 0.1) {
                exit('{"code":-1,"msg":"大佬，我得罪不起您，我小本生意，有哪里得罪之处，还望多多包涵"}');
            }

            $data = array("websiteNumber" => $number, "data" => array(array("username" => $username, "password" => $password)));
            $data = json_encode($data);
            $jxjy_url = $jk["url"] . "/api/order/buy";
            $result = post($jxjy_url, $data, $header);
            $result = json_decode($result, true);

            if ($result['code'] == 200) {
                // 获取易教育返回的订单号
                $order_data = $result['data'];
                if (isset($order_data['orderList']) && !empty($order_data['orderList'])) {
                    $order_info = $order_data['orderList'][0];
                    $yid = $order_info['orderId']; // 易教育返回的订单ID

                    // 插入到主订单表，设置dockstatus=1表示已提交到上游
                    $is_main = $DB->query("INSERT INTO qingka_wangke_order (uid,cid,hid,yid,ptname,school,user,pass,kcname,noun,status,process,addtime,dockstatus) VALUES ('{$userrow['uid']}','{$id}','{$jk['hid']}','{$yid}','{$rs['name']}','','$username','$password','','{$number}','队列中','0%','$now_time','1')");

                    if ($is_main) {
                        $DB->query("UPDATE qingka_wangke_user SET money=money-'{$danjia}' WHERE uid='{$userrow['uid']}' LIMIT 1");
                        wlog($userrow['uid'], "继续教育下单", "平台{$rs['name']} {$username} {$password}扣除{$danjia}元", -$danjia);
                        jsonReturn(1, "成功下单，订单号：{$yid}");
                    } else {
                        jsonReturn(-1, "数据库插入失败");
                    }
                } else {
                    jsonReturn(-1, "下单成功但未获取到订单号");
                }
            } else {
                $error_msg = isset($result['message']) ? $result['message'] : (isset($result['data']['message']) ? $result['data']['message'] : '下单失败');
                jsonReturn(-1, $error_msg);
            }
        } else {
            // 查课下单
            if (empty($select)) {
                jsonReturn(-1, "请选择课程");
            }
            $success = 0;
            foreach ($select as $item) {
                $kcid = $item['id'];
                $kcname = $item['name'];
                $danjia = round($rs['price'] * $userrow['addprice'], 2);
                if ($userrow['money'] < $danjia) {
                    jsonReturn(-1, "您的余额不足");
                }
                if ($danjia == 0 || $userrow['addprice'] < 0.1) {
                    exit('{"code":-1,"msg":"大佬，我得罪不起您，我小本生意，有哪里得罪之处，还望多多包涵"}');
                }

                if (strpos($kcname, '----') !== false) {
                    list($kcname1, $kcname2) = explode('----', $kcname);
                    $data = array("websiteNumber" => $number, "data" => array(array("username" => $username, "password" => $password, "name" => $username . "----" . $password, "children" => array(array("name" => $kcname1, "children" => array(array("name" => $kcname2, "disabled" => false, "id" => $kcid, "selected" => true)), "disabled" => true, "selected" => true)), "selected" => true)));
                } else {
                    $data = array("websiteNumber" => $number, "data" => array(array("username" => $username, "password" => $password, "name" => $username . "----" . $password, "children" => array(array("name" => $kcname, "disabled" => false, "id" => $kcid, "selected" => true)), "selected" => true)));
                }

                $data = json_encode($data);
                $jxjy_url = $jk["url"] . "/api/order/buy";
                $result = post($jxjy_url, $data, $header);
                $result = json_decode($result, true);

                if ($result['code'] == 200) {
                    // 获取易教育返回的订单号
                    $order_data = $result['data'];
                    if (isset($order_data['orderList']) && !empty($order_data['orderList'])) {
                        $order_info = $order_data['orderList'][0];
                        $yid = $order_info['orderId']; // 易教育返回的订单ID

                        // 插入到主订单表，设置dockstatus=1表示已提交到上游
                        $is_main = $DB->query("INSERT INTO qingka_wangke_order (uid,cid,hid,yid,ptname,school,user,pass,kcid,kcname,noun,status,process,addtime,dockstatus) VALUES ('{$userrow['uid']}','{$id}','{$jk['hid']}','{$yid}','{$rs['name']}','','$username','$password','$kcid','$kcname','{$number}','队列中','0%','$now_time','1')");

                        if ($is_main) {
                            $DB->query("UPDATE qingka_wangke_user SET money=money-'{$danjia}' WHERE uid='{$userrow['uid']}' LIMIT 1");
                            wlog($userrow['uid'], "继续教育下单", "平台{$rs['name']} {$username} {$password} {$kcname}扣除{$danjia}元", -$danjia);
                            $success++;
                        }
                    }
                } else {
                    $error_msg = isset($result['message']) ? $result['message'] : '下单失败';
                    // 继续处理下一个，不中断批量操作
                }
            }
            if ($success > 0) {
                exit('{"code":1,"msg":"提交成功，共提交' . $success . '条"}');
            } else {
                exit('{"code":-1,"msg":"提交失败"}');
            }
        }
        break;

    case 'jxjyorderlist':
        // 订单列表
        $cx = daddslashes($_POST['cx']);
        $page = trim(strip_tags(daddslashes($_POST['page'])));
        $pagesize = trim(strip_tags($cx['pagesize']));
        if ($pagesize == "") {
            $pagesize = 20;
        }
        $pageu = ($page - 1) * $pagesize;

        $qq = trim(strip_tags($cx['phone']));
        $status_text = trim(strip_tags($cx['status_text']));
        $dock = trim(strip_tags($cx['dock']));
        $oid = trim(strip_tags($cx['oid']));
        $uid = trim(strip_tags($cx['uid']));
        $serialNumber = trim(strip_tags($cx['serialNumber']));
        $status_text1 = trim(strip_tags($cx['status_text1']));

        if ($userrow['uid'] != '1') {
            $sql1 = "WHERE uid='{$userrow['uid']}'";
        } else {
            $sql1 = "WHERE 1=1";
        }

        if ($qq != '') {
            $sql3 = " AND user='{$qq}'";
        }
        if ($oid != '') {
            $sql4 = " AND oid='{$oid}'";
        }
        if ($uid != '') {
            $sql5 = " AND uid='{$uid}'";
        }
        if ($status_text != '') {
            $sql6 = " AND status='{$status_text}'";
        }
        if ($dock != '') {
            $sql7 = " AND dockstatus='{$dock}'";
        }
        if ($status_text1 != '') {
            $sql8 = " AND status='{$status_text1}'";
        }

        $sql = "SELECT * FROM qingka_wangke_jxjyorder {$sql1} {$sql3} {$sql4} {$sql5} {$sql6} {$sql7} {$sql8} ORDER BY oid DESC LIMIT {$pageu}, {$pagesize}";
        $a = $DB->query($sql);
        $data = array();
        while ($row = $DB->fetch($a)) {
            $data[] = $row;
        }

        $sql_count = "SELECT COUNT(*) as total FROM qingka_wangke_jxjyorder {$sql1} {$sql3} {$sql4} {$sql5} {$sql6} {$sql7} {$sql8}";
        $count_result = $DB->get_row($sql_count);
        $total = $count_result['total'];

        $response = array(
            'code' => 1,
            'data' => $data,
            'count' => $total,
            'page' => $page,
            'pagesize' => $pagesize
        );
        exit(json_encode($response));
        break;

    case 'sync_orders':
        // 易教育订单同步接口

        // 包含必要的函数文件
        if (!function_exists('get_url')) {
            // 手动定义 get_url 函数
            function get_url($url, $post = false, $cookie = false, $header = false) {
                $ch = curl_init();
                if ($header) {
                    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
                } else {
                    curl_setopt($ch, CURLOPT_HEADER, 0);
                }
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.62 Safari/537.36");
                if ($post) {
                    curl_setopt($ch, CURLOPT_POST, 1);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post));
                }
                if ($cookie) {
                    curl_setopt($ch, CURLOPT_COOKIE, $cookie);
                }
                $result = curl_exec($ch);
                curl_close($ch);
                return $result;
            }
        }
        if (!function_exists('processCx')) {
            include_once(dirname(__DIR__) . '/Checkorder/jdjk.php');
        }

        try {
            // 获取易教育货源信息
            $jk = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt='jxjy' LIMIT 1");
            if (!$jk) {
                jsonReturn(-1, "易教育货源配置不存在");
            }

            // 查询需要同步的易教育订单（从主订单表）
            $sql = "SELECT o.*, h.pt as platform_type
                    FROM qingka_wangke_order o
                    LEFT JOIN qingka_wangke_huoyuan h ON o.hid = h.hid
                    WHERE h.pt = 'jxjy'
                    AND o.dockstatus = 1
                    AND o.status NOT IN ('已完成', '已退款', '已取消')
                    AND o.addtime > DATE_SUB(NOW(), INTERVAL 7 DAY)
                    ORDER BY o.addtime DESC
                    LIMIT 10";

            $result = $DB->query($sql);
            $sync_count = 0;
            $error_count = 0;
            $orders_processed = 0;

            if (!$result) {
                jsonReturn(-1, "数据库查询失败");
            }

            while ($order = $DB->fetch($result)) {
                $orders_processed++;

                try {
                    // 调用进度查询函数
                    $progress_result = processCx($order['oid']);

                    if (!empty($progress_result) && is_array($progress_result)) {
                        foreach ($progress_result as $item) {
                            if (isset($item['code']) && $item['code'] == 1) {
                                // 更新订单状态
                                $status = isset($item['status_text']) ? $item['status_text'] : $order['status'];
                                $process = isset($item['process']) ? $item['process'] : $order['process'];
                                $remarks = isset($item['remarks']) ? $item['remarks'] : $order['remarks'];

                                // 🔥 关键修复：不要覆盖 yid 字段！
                                // 只有当原 yid 为空或为 "1" 时，才使用返回的 yid
                                $should_update_yid = false;
                                $new_yid = $order['yid']; // 默认保持原值

                                if (empty($order['yid']) || $order['yid'] == '1') {
                                    if (isset($item['yid']) && !empty($item['yid']) && $item['yid'] != '1') {
                                        $should_update_yid = true;
                                        $new_yid = $item['yid'];
                                    }
                                }

                                if ($should_update_yid) {
                                    $update_sql = "UPDATE qingka_wangke_order SET
                                                  status = ?,
                                                  process = ?,
                                                  remarks = ?,
                                                  yid = ?,
                                                  uptime = NOW()
                                                  WHERE oid = ?";
                                    $update_result = $DB->prepare_query($update_sql, [
                                        $status,
                                        $process,
                                        $remarks,
                                        $new_yid,
                                        $order['oid']
                                    ]);
                                } else {
                                    // 不更新 yid，保持原有订单号
                                    $update_sql = "UPDATE qingka_wangke_order SET
                                                  status = ?,
                                                  process = ?,
                                                  remarks = ?,
                                                  uptime = NOW()
                                                  WHERE oid = ?";
                                    $update_result = $DB->prepare_query($update_sql, [
                                        $status,
                                        $process,
                                        $remarks,
                                        $order['oid']
                                    ]);
                                }

                                if ($update_result) {
                                    $sync_count++;
                                } else {
                                    $error_count++;
                                }
                            } else {
                                $error_count++;
                            }
                        }
                    } else {
                        $error_count++;
                    }
                } catch (Exception $e) {
                    $error_count++;
                    error_log("易教育订单同步错误 - 订单ID: {$order['oid']}, 错误: " . $e->getMessage());
                }

                // 避免请求过于频繁
                usleep(200000); // 0.2秒延迟
            }

            jsonReturn(1, "同步完成，处理订单: {$orders_processed}，成功: {$sync_count}，失败: {$error_count}");

        } catch (Exception $e) {
            jsonReturn(-1, "同步失败: " . $e->getMessage());
        }
        break;

    default:
        // 默认响应
        $result = array(
            "code" => 1,
            "msg" => "易教育API接口",
            "actions" => array(
                "jxjyclass" => "获取项目列表",
                "getcourse" => "查课接口",
                "jxjyadd" => "下单接口",
                "jxjyorderlist" => "订单列表",
                "sync_orders" => "同步订单状态"
            )
        );
        exit(json_encode($result));
        break;
}
?>

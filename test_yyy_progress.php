<?php
// yyy教育进度同步测试脚本
// 用于测试和调试yyy教育的进度同步功能

// 显示所有错误
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入必要的文件
include('confing/common.php');
include('Checkorder/configuration.php');

echo "=== yyy教育进度同步测试 ===\n\n";

// 1. 检查yyy教育货源配置
echo "1. 检查yyy教育货源配置...\n";
$yyy_huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'yyy' LIMIT 1");

if (!$yyy_huoyuan) {
    echo "❌ 没有找到yyy教育货源配置\n";
    exit;
} else {
    echo "✅ 找到yyy教育货源配置\n";
    echo "   货源ID: {$yyy_huoyuan['hid']}\n";
    echo "   名称: {$yyy_huoyuan['name']}\n";
    echo "   URL: {$yyy_huoyuan['url']}\n";
    echo "   账号: {$yyy_huoyuan['user']}\n";
}

// 2. 查询yyy教育订单
echo "\n2. 查询yyy教育订单...\n";
$sql = "SELECT o.*, h.pt as platform_type 
        FROM qingka_wangke_order o 
        LEFT JOIN qingka_wangke_huoyuan h ON o.hid = h.hid 
        WHERE h.pt = 'yyy' 
        ORDER BY o.addtime DESC 
        LIMIT 5";

$result = $DB->query($sql);
$orders = [];
while ($order = $DB->fetch($result)) {
    $orders[] = $order;
}

if (empty($orders)) {
    echo "❌ 没有找到yyy教育订单\n";
    echo "提示：请先创建一些yyy教育订单进行测试\n";
    exit;
} else {
    echo "✅ 找到 " . count($orders) . " 个yyy教育订单\n";
    foreach ($orders as $order) {
        echo "   订单ID: {$order['oid']}, 用户: {$order['user']}, 状态: {$order['status']}, 进度: {$order['process']}\n";
    }
}

// 3. 包含进度查询函数
echo "\n3. 加载进度查询函数...\n";
include('Checkorder/jdjk.php');
echo "✅ 进度查询函数加载成功\n";

// 4. 测试进度查询
echo "\n4. 测试进度查询...\n";
$test_order = $orders[0]; // 使用第一个订单进行测试

echo "测试订单信息:\n";
echo "   订单ID: {$test_order['oid']}\n";
echo "   用户: {$test_order['user']}\n";
echo "   课程: {$test_order['kcname']}\n";
echo "   当前状态: {$test_order['status']}\n";
echo "   当前进度: {$test_order['process']}\n";
echo "   YID: {$test_order['yid']}\n";

echo "\n正在查询进度...\n";
try {
    $progress_result = processCx($test_order['oid']);
    
    echo "\n5. 查询结果:\n";
    if (is_array($progress_result) && !empty($progress_result)) {
        foreach ($progress_result as $i => $item) {
            echo "   结果 " . ($i + 1) . ":\n";
            if (isset($item['code'])) {
                echo "     返回码: {$item['code']}\n";
                echo "     消息: {$item['msg']}\n";
                
                if ($item['code'] == 1) {
                    echo "     YID: {$item['yid']}\n";
                    echo "     状态: {$item['status_text']}\n";
                    echo "     进度: {$item['process']}\n";
                    echo "     课程: {$item['kcname']}\n";
                    echo "     用户: {$item['user']}\n";
                    echo "     备注: {$item['remarks']}\n";
                    
                    // 检查进度是否为1%的问题
                    if ($item['process'] == '1%' || $item['process'] == '1') {
                        echo "     ⚠️ 发现进度显示为1%的问题！\n";
                        echo "     原始备注信息: {$item['remarks']}\n";
                        
                        // 分析备注中的进度信息
                        if (preg_match('/(\d+(?:\.\d+)?)%/', $item['remarks'], $matches)) {
                            echo "     从备注中提取的进度: {$matches[1]}%\n";
                        } else {
                            echo "     备注中没有找到进度信息\n";
                        }
                    }
                } else {
                    echo "     ❌ 查询失败: {$item['msg']}\n";
                }
            } else {
                echo "     异常结果: ";
                print_r($item);
            }
        }
    } else {
        echo "   ❌ 查询返回空结果或非数组\n";
        echo "   返回值: ";
        var_dump($progress_result);
    }
    
} catch (Exception $e) {
    echo "   ❌ 查询异常: " . $e->getMessage() . "\n";
}

// 6. 测试API连接
echo "\n6. 测试yyy教育API连接...\n";
$test_data = array(
    "uid" => $yyy_huoyuan["user"], 
    "key" => $yyy_huoyuan["pass"], 
    "platform" => "2864", // 使用通用的lastoid
    "school" => "", 
    "user" => "test", 
    "pass" => "test", 
    "kcname" => "test", 
    "yid" => "test123"
);

$test_url = "{$yyy_huoyuan["url"]}/api/getorder";

echo "请求URL: {$test_url}\n";
echo "请求数据: " . json_encode($test_data) . "\n";

$api_result = get_url($test_url, $test_data);
echo "API响应: {$api_result}\n";

$api_data = json_decode($api_result, true);
if ($api_data) {
    echo "解析后的响应:\n";
    echo "   代码: " . (isset($api_data['code']) ? $api_data['code'] : '未知') . "\n";
    echo "   消息: " . (isset($api_data['message']) ? $api_data['message'] : '无消息') . "\n";
    
    if (isset($api_data['data']['list']) && is_array($api_data['data']['list'])) {
        echo "   订单数量: " . count($api_data['data']['list']) . "\n";
        
        if (!empty($api_data['data']['list'])) {
            $sample_order = $api_data['data']['list'][0];
            echo "   示例订单:\n";
            foreach ($sample_order as $key => $value) {
                echo "     {$key}: {$value}\n";
            }
        }
    }
} else {
    echo "❌ API响应解析失败\n";
}

echo "\n=== 测试完成 ===\n";
?>
